import { googleAuth } from "../auth/google-auth.js";

/**
 * Google Drive API Client
 * Enhanced wrapper cho migration operations với comprehensive features
 */
export class GoogleDriveAPI {
  constructor() {
    this.auth = googleAuth;

    // Statistics tracking
    this.stats = {
      apiCalls: 0,
      errors: 0,
      filesProcessed: 0,
      bytesTransferred: 0,
    };

    // Rate limiting
    this.rateLimiter = {
      requests: 0,
      resetTime: Date.now() + 60000,
      maxRequests: 1000, // Google Drive API limit
    };

    // Cache for frequently accessed data
    this.cache = new Map();
  }

  /**
   * Check and enforce rate limiting
   */
  checkRateLimit() {
    const now = Date.now();

    if (now > this.rateLimiter.resetTime) {
      this.rateLimiter.requests = 0;
      this.rateLimiter.resetTime = now + 60000;
    }

    if (this.rateLimiter.requests >= this.rateLimiter.maxRequests) {
      throw new Error(
        "Google Drive API rate limit exceeded. Please wait before making more requests."
      );
    }

    this.rateLimiter.requests++;
    this.stats.apiCalls++;
  }

  /**
   * <PERSON><PERSON><PERSON> danh sách files từ Drive với enhanced features
   * @param {string} userEmail - Email người dùng
   * @param {object} options - Query options
   * @returns {Promise<object>} Drive files response với metadata
   */
  async listFiles(userEmail, options = {}) {
    try {
      this.checkRateLimit();

      const drive = await this.auth.getDriveClient(userEmail);

      const defaultOptions = {
        q: "trashed=false",
        fields:
          "nextPageToken, files(id, name, mimeType, size, parents, createdTime, modifiedTime, owners, permissions, webViewLink, iconLink, thumbnailLink)",
        supportsAllDrives: true,
        includeItemsFromAllDrives: true,
        pageSize: 100,
        orderBy: "modifiedTime desc",
      };

      const queryOptions = { ...defaultOptions, ...options };

      console.log(
        `📁 Listing files for ${userEmail} with query: ${queryOptions.q}`
      );

      const startTime = Date.now();
      const response = await drive.files.list(queryOptions);
      const duration = Date.now() - startTime;

      const files = response.data.files || [];
      this.stats.filesProcessed += files.length;

      // If no files found, provide diagnostic information
      if (files.length === 0) {
        console.log(
          `⚠️ No files found for ${userEmail}. Running diagnostics...`
        );
        await this.diagnoseDriveAccess(userEmail, drive);
      }

      // Add computed properties to files
      const enhancedFiles = files.map((file) => ({
        ...file,
        sizeFormatted: this.formatFileSize(file.size),
        isGoogleDoc: this.isGoogleDocsFormat(file.mimeType),
        path: null, // Will be computed later if needed
        permissions: file.permissions || [],
      }));

      console.log(`✅ Found ${enhancedFiles.length} files in ${duration}ms`);

      return {
        ...response.data,
        files: enhancedFiles,
        metadata: {
          totalFiles: enhancedFiles.length,
          totalSize: enhancedFiles.reduce(
            (sum, file) => sum + (parseInt(file.size) || 0),
            0
          ),
          googleDocs: enhancedFiles.filter((file) => file.isGoogleDoc).length,
          queryTime: duration,
          userEmail,
        },
      };
    } catch (error) {
      this.stats.errors++;
      console.error("❌ Error listing files:", error.message);

      // Provide specific error guidance
      if (error.message.includes("unauthorized_client")) {
        throw new Error(
          `Domain-wide delegation not configured for ${userEmail}. Please enable it in Google Admin Console.`
        );
      } else if (error.message.includes("insufficient permission")) {
        throw new Error(
          `Insufficient permissions for ${userEmail}. User may not be in the organization or lacks Drive access.`
        );
      } else if (error.message.includes("invalid_grant")) {
        throw new Error(
          `Invalid user email ${userEmail}. Check if user exists in the organization.`
        );
      }

      throw new Error(
        `Failed to list files for ${userEmail}: ${error.message}`
      );
    }
  }

  /**
   * Lấy danh sách users từ Google Workspace Directory API
   * @param {object} options - Query options (pageSize, query, fields, etc.)
   * @returns {Promise<object>} Users response với metadata
   */
  async listUsers(options = {}) {
    try {
      this.checkRateLimit();

      // Sử dụng Directory API client thay vì Drive API client
      // Ưu tiên lấy adminEmail từ options, nếu không có thì dùng serviceAccountEmail
      const adminEmail = process.env.GOOGLE_ADMIN_EMAIL;
      const directory = await this.auth.getAdminClient(adminEmail);

      const defaultOptions = {
        customer: "my_customer",
        maxResults: 500,
        orderBy: "email",
        projection: "basic",
        fields:
          "users(id,primaryEmail,name,suspended,lastLoginTime),nextPageToken",
      };

      const queryOptions = { ...defaultOptions, ...options };

      console.log(
        `👥 Listing users with options: ${JSON.stringify(queryOptions)}`
      );

      const startTime = Date.now();
      const response = await directory.users.list(queryOptions);
      const duration = Date.now() - startTime;

      const users = response.data.users || [];

      console.log(`✅ Found ${users.length} users in ${duration}ms`);

      return {
        ...response.data,
        users: users,
        metadata: {
          totalUsers: users.length,
          queryTime: duration,
        },
      };
    } catch (error) {
      this.stats.errors++;
      console.error("❌ Error listing users:", error.message);

      // Log detailed error information for debugging
      if (error.response?.data?.error) {
        console.error(
          "❌ Google API Error Details:",
          JSON.stringify(error.response.data.error, null, 2)
        );
      }

      // Check for specific error types
      if (
        error.message.includes("notAuthorizedToAccessUserData") ||
        error.response?.data?.error?.code === 403
      ) {
        throw new Error(
          "Insufficient admin privileges to list users. Please ensure:\n" +
            "1. Service account has domain-wide delegation enabled\n" +
            "2. Admin directory scope is authorized in Google Admin Console\n" +
            "3. The admin email has proper Directory API permissions"
        );
      }

      if (error.response?.data?.error?.code === 400) {
        throw new Error(
          "Bad Request to Directory API. Please check:\n" +
            "1. Domain-wide delegation is properly configured\n" +
            "2. Service account has correct scopes\n" +
            "3. Admin email is valid and has directory access\n" +
            "Error: " +
            error.message
        );
      }

      throw new Error(`Failed to list users: ${error.message}`);
    }
  }

  /**
   * Diagnose Drive access issues when no files are returned
   * @param {string} userEmail - User email
   * @param {object} drive - Drive client
   */
  async diagnoseDriveAccess(userEmail, drive) {
    console.log(`🔍 Diagnosing Drive access for ${userEmail}...`);

    try {
      // 1. Check user info
      const about = await drive.about.get({ fields: "user,storageQuota" });
      console.log(`   ✅ User info: ${about.data.user.emailAddress}`);
      console.log(
        `   📊 Storage used: ${this.formatFileSize(
          about.data.storageQuota.usage
        )}`
      );

      // 2. Check if user has any files at all (including trashed)
      const allFilesResponse = await drive.files.list({
        pageSize: 1,
        fields: "files(id,name,trashed)",
      });

      const totalFiles = allFilesResponse.data.files || [];
      if (totalFiles.length === 0) {
        console.log(`   ⚠️ User has no files in Drive at all`);
      } else {
        console.log(
          `   📁 User has ${totalFiles.length} files (including trashed)`
        );

        // Check if all files are trashed
        const trashedResponse = await drive.files.list({
          pageSize: 10,
          q: "trashed=true",
          fields: "files(id,name)",
        });

        const trashedFiles = trashedResponse.data.files || [];
        if (trashedFiles.length > 0) {
          console.log(`   🗑️ Found ${trashedFiles.length} trashed files`);
        }
      }

      // 3. Check shared drives
      try {
        const sharedDrives = await drive.drives.list({ pageSize: 10 });
        const drives = sharedDrives.data.drives || [];
        console.log(`   🌐 User has access to ${drives.length} shared drives`);

        if (drives.length > 0) {
          // Check files in shared drives
          const sharedFilesResponse = await drive.files.list({
            pageSize: 5,
            q: "trashed=false",
            supportsAllDrives: true,
            includeItemsFromAllDrives: true,
            corpora: "allDrives",
          });
          const sharedFiles = sharedFilesResponse.data.files || [];
          console.log(
            `   📁 Found ${sharedFiles.length} files in shared drives`
          );
        }
      } catch (sharedError) {
        console.log(
          `   ⚠️ Cannot access shared drives: ${sharedError.message}`
        );
      }

      // 4. Check specific folder access
      try {
        const rootResponse = await drive.files.list({
          pageSize: 5,
          q: "'root' in parents and trashed=false",
        });
        const rootFiles = rootResponse.data.files || [];
        console.log(`   📁 Found ${rootFiles.length} files in root folder`);
      } catch (rootError) {
        console.log(`   ⚠️ Cannot access root folder: ${rootError.message}`);
      }
    } catch (diagError) {
      console.log(`   ❌ Diagnostic failed: ${diagError.message}`);
    }
  }

  /**
   * Lấy tất cả files với pagination
   * @param {string} userEmail - Email người dùng
   * @param {object} options - Query options
   * @param {function} progressCallback - Callback để báo cáo tiến trình
   * @returns {Promise<Array>} Tất cả files
   */
  async getAllFiles(userEmail, options = {}, progressCallback = null) {
    const allFiles = [];
    let nextPageToken = null;
    let pageCount = 0;

    try {
      do {
        const pageOptions = {
          ...options,
          pageToken: nextPageToken,
        };

        const response = await this.listFiles(userEmail, pageOptions);
        allFiles.push(...response.files);
        nextPageToken = response.nextPageToken;
        pageCount++;

        if (progressCallback) {
          progressCallback({
            page: pageCount,
            filesFound: allFiles.length,
            hasMore: !!nextPageToken,
          });
        }

        console.log(
          `📄 Page ${pageCount}: ${response.files.length} files (Total: ${allFiles.length})`
        );

        // Small delay to respect rate limits
        if (nextPageToken) {
          await new Promise((resolve) => setTimeout(resolve, 100));
        }
      } while (nextPageToken);

      console.log(
        `✅ Retrieved all ${allFiles.length} files in ${pageCount} pages`
      );
      return allFiles;
    } catch (error) {
      this.stats.errors++;
      console.error("❌ Error getting all files:", error.message);
      throw new Error(
        `Failed to get all files for ${userEmail}: ${error.message}`
      );
    }
  }

  /**
   * Lấy tất cả users với pagination
   * @param {object} options - Query options
   * @param {function} progressCallback - Callback để báo cáo tiến trình
   * @returns {Promise<Array>} Tất cả users
   */
  async getAllUsers(options = {}, progressCallback = null) {
    const allUsers = [];
    let nextPageToken = null;
    let pageCount = 0;

    try {
      do {
        const pageOptions = {
          ...options,
          pageToken: nextPageToken,
        };

        const response = await this.listUsers(pageOptions);
        allUsers.push(...response.users);
        nextPageToken = response.nextPageToken;
        pageCount++;

        if (progressCallback) {
          progressCallback({
            page: pageCount,
            usersFound: allUsers.length,
            hasMore: !!nextPageToken,
          });
        }

        console.log(
          `👥 Page ${pageCount}: ${response.users.length} users (Total: ${allUsers.length})`
        );

        // Small delay to respect rate limits
        if (nextPageToken) {
          await new Promise((resolve) => setTimeout(resolve, 100));
        }
      } while (nextPageToken);

      console.log(
        `✅ Retrieved all ${allUsers.length} users in ${pageCount} pages`
      );
      return allUsers;
    } catch (error) {
      this.stats.errors++;
      console.error("❌ Error getting all users:", error.message);
      throw new Error(`Failed to get all users: ${error.message}`);
    }
  }

  /**
   * Lấy thông tin chi tiết của một file với caching
   * @param {string} userEmail - Email người dùng
   * @param {string} fileId - ID của file
   * @param {string} fields - Fields cần lấy
   * @param {boolean} useCache - Sử dụng cache hay không
   * @returns {Promise<object>} Enhanced file metadata
   */
  async getFile(userEmail, fileId, fields = null, useCache = true) {
    try {
      this.checkRateLimit();

      const cacheKey = `file_${fileId}_${userEmail}`;

      // Check cache
      if (useCache && this.cache.has(cacheKey)) {
        const cached = this.cache.get(cacheKey);
        if (cached.expires > Date.now()) {
          console.log(`🔄 Using cached file data for ${fileId}`);
          return cached.data;
        }
      }

      const defaultFields =
        "id, name, mimeType, size, parents, createdTime, modifiedTime, owners, permissions, webViewLink, iconLink, thumbnailLink, exportLinks, downloadUrl";
      const fieldsToUse = fields || defaultFields;

      const drive = await this.auth.getDriveClient(userEmail);

      const startTime = Date.now();
      const response = await drive.files.get({
        fileId,
        fields: fieldsToUse,
        supportsAllDrives: true,
      });
      const duration = Date.now() - startTime;

      // Enhance file data
      const fileData = {
        ...response.data,
        sizeFormatted: this.formatFileSize(response.data.size),
        isGoogleDoc: this.isGoogleDocsFormat(response.data.mimeType),
        queryTime: duration,
        retrievedAt: new Date().toISOString(),
      };

      // Cache the result
      if (useCache) {
        this.cache.set(cacheKey, {
          data: fileData,
          expires: Date.now() + 10 * 60 * 1000, // 10 minutes
        });
      }

      console.log(`📄 Retrieved file ${fileData.name} in ${duration}ms`);
      return fileData;
    } catch (error) {
      this.stats.errors++;
      console.error(`❌ Error getting file ${fileId}:`, error.message);
      throw new Error(`Failed to get file ${fileId}: ${error.message}`);
    }
  }

  /**
   * Lấy thông tin nhiều files cùng lúc
   * @param {string} userEmail - Email người dùng
   * @param {Array<string>} fileIds - Danh sách file IDs
   * @param {function} progressCallback - Callback báo cáo tiến trình
   * @returns {Promise<Array>} Danh sách file metadata
   */
  async getMultipleFiles(userEmail, fileIds, progressCallback = null) {
    const results = [];
    const errors = [];

    try {
      console.log(`📁 Getting ${fileIds.length} files for ${userEmail}`);

      for (let i = 0; i < fileIds.length; i++) {
        try {
          const fileData = await this.getFile(userEmail, fileIds[i]);
          results.push(fileData);

          if (progressCallback) {
            progressCallback({
              current: i + 1,
              total: fileIds.length,
              file: fileData,
              errors: errors.length,
            });
          }

          // Small delay to respect rate limits
          if (i < fileIds.length - 1) {
            await new Promise((resolve) => setTimeout(resolve, 50));
          }
        } catch (error) {
          errors.push({
            fileId: fileIds[i],
            error: error.message,
          });
          console.error(`❌ Failed to get file ${fileIds[i]}:`, error.message);
        }
      }

      console.log(
        `✅ Retrieved ${results.length} files, ${errors.length} errors`
      );
      return {
        files: results,
        errors,
        stats: {
          total: fileIds.length,
          success: results.length,
          failed: errors.length,
        },
      };
    } catch (error) {
      this.stats.errors++;
      throw new Error(`Failed to get multiple files: ${error.message}`);
    }
  }

  /**
   * Lấy danh sách permissions của file với enhanced data
   * @param {string} userEmail - Email người dùng
   * @param {string} fileId - ID của file
   * @param {boolean} useCache - Sử dụng cache hay không
   * @returns {Promise<object>} Enhanced permissions data
   */
  async getFilePermissions(userEmail, fileId, useCache = true) {
    try {
      this.checkRateLimit();

      const cacheKey = `permissions_${fileId}_${userEmail}`;

      // Check cache
      if (useCache && this.cache.has(cacheKey)) {
        const cached = this.cache.get(cacheKey);
        if (cached.expires > Date.now()) {
          console.log(`🔄 Using cached permissions for ${fileId}`);
          return cached.data;
        }
      }

      const drive = await this.auth.getDriveClient(userEmail);

      const startTime = Date.now();
      const response = await drive.permissions.list({
        fileId,
        fields:
          "permissions(id, type, emailAddress, role, displayName, domain, allowFileDiscovery, expirationTime)",
        supportsAllDrives: true,
      });
      const duration = Date.now() - startTime;

      const permissions = response.data.permissions || [];

      // Enhance permissions data
      const enhancedPermissions = permissions.map((perm) => ({
        ...perm,
        isOwner: perm.role === "owner",
        isPublic: perm.type === "anyone",
        isDomain: perm.type === "domain",
        isUser: perm.type === "user",
        isGroup: perm.type === "group",
        identifier: perm.emailAddress || perm.domain || perm.type,
      }));

      // Analyze permissions
      const analysis = {
        total: enhancedPermissions.length,
        owners: enhancedPermissions.filter((p) => p.isOwner).length,
        editors: enhancedPermissions.filter((p) => p.role === "writer").length,
        viewers: enhancedPermissions.filter((p) => p.role === "reader").length,
        commenters: enhancedPermissions.filter((p) => p.role === "commenter")
          .length,
        publicAccess: enhancedPermissions.some((p) => p.isPublic),
        domainAccess: enhancedPermissions.some((p) => p.isDomain),
        externalUsers: enhancedPermissions.filter(
          (p) =>
            p.isUser &&
            p.emailAddress &&
            !p.emailAddress.includes("@your-domain.com")
        ).length,
      };

      const result = {
        permissions: enhancedPermissions,
        analysis,
        metadata: {
          fileId,
          queryTime: duration,
          retrievedAt: new Date().toISOString(),
        },
      };

      // Cache the result
      if (useCache) {
        this.cache.set(cacheKey, {
          data: result,
          expires: Date.now() + 5 * 60 * 1000, // 5 minutes
        });
      }

      console.log(
        `🔐 Retrieved ${permissions.length} permissions for ${fileId} in ${duration}ms`
      );
      return result;
    } catch (error) {
      this.stats.errors++;
      console.error(
        `❌ Error getting permissions for file ${fileId}:`,
        error.message
      );
      throw new Error(
        `Failed to get permissions for file ${fileId}: ${error.message}`
      );
    }
  }

  /**
   * Lấy permissions cho nhiều files
   * @param {string} userEmail - Email người dùng
   * @param {Array<string>} fileIds - Danh sách file IDs
   * @param {function} progressCallback - Callback báo cáo tiến trình
   * @returns {Promise<object>} Permissions data cho tất cả files
   */
  async getMultipleFilePermissions(
    userEmail,
    fileIds,
    progressCallback = null
  ) {
    const results = {};
    const errors = [];

    try {
      console.log(`🔐 Getting permissions for ${fileIds.length} files`);

      for (let i = 0; i < fileIds.length; i++) {
        const fileId = fileIds[i];

        try {
          const permissionsData = await this.getFilePermissions(
            userEmail,
            fileId
          );
          results[fileId] = permissionsData;

          if (progressCallback) {
            progressCallback({
              current: i + 1,
              total: fileIds.length,
              fileId,
              permissions: permissionsData.permissions.length,
              errors: errors.length,
            });
          }

          // Small delay to respect rate limits
          if (i < fileIds.length - 1) {
            await new Promise((resolve) => setTimeout(resolve, 100));
          }
        } catch (error) {
          errors.push({
            fileId,
            error: error.message,
          });
          console.error(
            `❌ Failed to get permissions for ${fileId}:`,
            error.message
          );
        }
      }

      // Aggregate analysis
      const aggregateAnalysis = {
        totalFiles: fileIds.length,
        successfulFiles: Object.keys(results).length,
        failedFiles: errors.length,
        totalPermissions: Object.values(results).reduce(
          (sum, data) => sum + data.permissions.length,
          0
        ),
        filesWithPublicAccess: Object.values(results).filter(
          (data) => data.analysis.publicAccess
        ).length,
        filesWithDomainAccess: Object.values(results).filter(
          (data) => data.analysis.domainAccess
        ).length,
        filesWithExternalUsers: Object.values(results).filter(
          (data) => data.analysis.externalUsers > 0
        ).length,
      };

      console.log(
        `✅ Retrieved permissions for ${Object.keys(results).length} files, ${
          errors.length
        } errors`
      );

      return {
        results,
        errors,
        analysis: aggregateAnalysis,
      };
    } catch (error) {
      this.stats.errors++;
      throw new Error(
        `Failed to get multiple file permissions: ${error.message}`
      );
    }
  }

  /**
   * Download file content
   * @param {string} userEmail - Email người dùng
   * @param {string} fileId - ID của file
   * @param {string} mimeType - MIME type của file
   * @returns {Promise<Buffer>} File content
   */
  async downloadFile(userEmail, fileId, mimeType) {
    try {
      const drive = await this.auth.getDriveClient(userEmail);

      let response;

      // Kiểm tra nếu là Google Docs format cần export
      if (this.isGoogleDocsFormat(mimeType)) {
        const exportMimeType = this.getExportMimeType(mimeType);
        console.log(`📄 Exporting Google Doc ${fileId} as ${exportMimeType}`);

        response = await drive.files.export({
          fileId,
          mimeType: exportMimeType,
        });
      } else {
        console.log(`📁 Downloading binary file ${fileId}`);

        response = await drive.files.get({
          fileId,
          alt: "media",
          supportsAllDrives: true,
        });
      }

      return response.data;
    } catch (error) {
      console.error(`❌ Error downloading file ${fileId}:`, error.message);
      throw new Error(`Failed to download file ${fileId}: ${error.message}`);
    }
  }

  /**
   * Format file size to human readable string
   * @param {string|number} bytes - File size in bytes
   * @returns {string} Formatted size
   */
  formatFileSize(bytes) {
    if (!bytes || bytes === "0") return "0 B";

    const k = 1024;
    const sizes = ["B", "KB", "MB", "GB", "TB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  }

  /**
   * Kiểm tra xem có phải Google Docs format không
   * @param {string} mimeType - MIME type
   * @returns {boolean} True nếu là Google Docs format
   */
  isGoogleDocsFormat(mimeType) {
    const googleDocsMimeTypes = [
      "application/vnd.google-apps.document",
      "application/vnd.google-apps.spreadsheet",
      "application/vnd.google-apps.presentation",
      "application/vnd.google-apps.drawing",
      "application/vnd.google-apps.form",
      "application/vnd.google-apps.script",
    ];
    return googleDocsMimeTypes.includes(mimeType);
  }

  /**
   * Lấy MIME type để export Google Docs
   * @param {string} googleMimeType - Google Docs MIME type
   * @returns {string} Export MIME type
   */
  getExportMimeType(googleMimeType) {
    const exportMap = {
      "application/vnd.google-apps.document":
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      "application/vnd.google-apps.spreadsheet":
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      "application/vnd.google-apps.presentation":
        "application/vnd.openxmlformats-officedocument.presentationml.presentation",
      "application/vnd.google-apps.drawing": "image/png",
      "application/vnd.google-apps.form": "application/pdf",
      "application/vnd.google-apps.script": "application/json",
    };
    return exportMap[googleMimeType] || "application/pdf";
  }

  /**
   * Get file extension for export format
   * @param {string} mimeType - Export MIME type
   * @returns {string} File extension
   */
  getFileExtension(mimeType) {
    const extensionMap = {
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
        ".docx",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":
        ".xlsx",
      "application/vnd.openxmlformats-officedocument.presentationml.presentation":
        ".pptx",
      "image/png": ".png",
      "application/pdf": ".pdf",
      "application/json": ".json",
      "text/plain": ".txt",
      "text/html": ".html",
    };
    return extensionMap[mimeType] || "";
  }

  /**
   * Build file path from parents
   * @param {string} userEmail - Email người dùng
   * @param {string} fileId - File ID
   * @param {Array} parents - Parent folder IDs
   * @returns {Promise<string>} Full file path
   */
  async buildFilePath(userEmail, fileId, parents = []) {
    try {
      if (!parents || parents.length === 0) {
        return "/";
      }

      const pathParts = [];
      let currentParent = parents[0];

      // Traverse up the folder hierarchy
      while (currentParent && currentParent !== "root") {
        try {
          const parentData = await this.getFile(
            userEmail,
            currentParent,
            "id, name, parents"
          );
          pathParts.unshift(parentData.name);
          currentParent = parentData.parents?.[0];
        } catch (error) {
          console.warn(
            `⚠️ Could not get parent folder ${currentParent}:`,
            error.message
          );
          break;
        }
      }

      return "/" + pathParts.join("/");
    } catch (error) {
      console.warn(
        `⚠️ Could not build path for file ${fileId}:`,
        error.message
      );
      return "/unknown";
    }
  }

  /**
   * Get statistics
   * @returns {object} API usage statistics
   */
  getStats() {
    return {
      ...this.stats,
      rateLimiter: {
        requests: this.rateLimiter.requests,
        resetTime: new Date(this.rateLimiter.resetTime),
        remaining: this.rateLimiter.maxRequests - this.rateLimiter.requests,
      },
      cacheSize: this.cache.size,
    };
  }

  /**
   * Clear cache
   */
  clearCache() {
    this.cache.clear();
    console.log("🧹 Google Drive API cache cleared");
  }

  /**
   * Reset statistics
   */
  resetStats() {
    this.stats = {
      apiCalls: 0,
      errors: 0,
      filesProcessed: 0,
      bytesTransferred: 0,
    };
    console.log("📊 Google Drive API stats reset");
  }

  /**
   * Comprehensive API testing
   * @param {string} userEmail - Email để test
   * @returns {Promise<object>} Detailed test results
   */
  async testAPIs(userEmail) {
    const results = {
      success: false,
      connection: false,
      operations: {
        listFiles: false,
        getFile: false,
        getPermissions: false,
        getAllFiles: false,
        multipleFiles: false,
      },
      performance: {},
      stats: {},
      errors: [],
    };

    try {
      console.log(`🔍 Testing Google Drive APIs for ${userEmail}...`);

      // Test 1: Connection
      console.log("1. Testing connection...");
      const connectionStart = Date.now();
      const connectionResult = await this.auth.testConnection(userEmail);
      results.connection = connectionResult.success;
      results.performance.connection = Date.now() - connectionStart;

      if (!results.connection) {
        results.errors.push("Connection failed");
        return results;
      }

      console.log(
        `✅ Connection successful in ${results.performance.connection}ms`
      );

      // Test 2: List files
      console.log("2. Testing files.list API...");
      const listStart = Date.now();
      const filesResponse = await this.listFiles(userEmail, { pageSize: 10 });
      results.operations.listFiles = !!(
        filesResponse.files && filesResponse.files.length >= 0
      );
      results.performance.listFiles = Date.now() - listStart;

      console.log(
        `✅ Listed ${filesResponse.files.length} files in ${results.performance.listFiles}ms`
      );

      if (filesResponse.files && filesResponse.files.length > 0) {
        const testFile = filesResponse.files[0];
        console.log(
          `📄 Using test file: ${testFile.name} (${testFile.sizeFormatted})`
        );

        // Test 3: Get file details
        console.log("3. Testing files.get API...");
        const getFileStart = Date.now();
        const fileData = await this.getFile(userEmail, testFile.id);
        results.operations.getFile = !!fileData.id;
        results.performance.getFile = Date.now() - getFileStart;

        console.log(
          `✅ Retrieved file details in ${results.performance.getFile}ms`
        );

        // Test 4: Get permissions
        console.log("4. Testing permissions.list API...");
        const permissionsStart = Date.now();
        const permissionsData = await this.getFilePermissions(
          userEmail,
          testFile.id
        );
        results.operations.getPermissions = !!(
          permissionsData.permissions &&
          Array.isArray(permissionsData.permissions)
        );
        results.performance.getPermissions = Date.now() - permissionsStart;

        console.log(
          `✅ Retrieved ${permissionsData.permissions.length} permissions in ${results.performance.getPermissions}ms`
        );

        // Test 5: Multiple files (if we have enough)
        if (filesResponse.files.length >= 3) {
          console.log("5. Testing multiple files operations...");
          const multipleStart = Date.now();
          const testFileIds = filesResponse.files.slice(0, 3).map((f) => f.id);
          const multipleResult = await this.getMultipleFiles(
            userEmail,
            testFileIds
          );
          results.operations.multipleFiles = multipleResult.files.length > 0;
          results.performance.multipleFiles = Date.now() - multipleStart;

          console.log(
            `✅ Retrieved ${multipleResult.files.length} files in batch in ${results.performance.multipleFiles}ms`
          );
        }

        // Test 6: Get all files (limited)
        console.log("6. Testing get all files (limited)...");
        const getAllStart = Date.now();
        const allFiles = await this.getAllFiles(userEmail, { pageSize: 5 });
        results.operations.getAllFiles = allFiles.length > 0;
        results.performance.getAllFiles = Date.now() - getAllStart;

        console.log(
          `✅ Retrieved all ${allFiles.length} files in ${results.performance.getAllFiles}ms`
        );
      }

      // Get final stats
      results.stats = this.getStats();
      results.success = Object.values(results.operations).every(
        (op) => op === true
      );

      console.log(`🎉 API testing completed! Success: ${results.success}`);
    } catch (error) {
      results.errors.push(error.message);
      console.error("❌ API test error:", error.message);
    }

    return results;
  }

  /**
   * Performance benchmark
   * @param {string} userEmail - Email để test
   * @returns {Promise<object>} Benchmark results
   */
  async benchmark(userEmail) {
    console.log("🏃 Running Google Drive API performance benchmark...");

    const results = {
      operations: {},
      cachePerformance: {},
      stats: {},
    };

    try {
      // Clear cache for fair test
      this.clearCache();
      this.resetStats();

      // Benchmark 1: List files
      console.log("1. Benchmarking list files...");
      const listTimes = [];
      for (let i = 0; i < 3; i++) {
        const start = Date.now();
        await this.listFiles(userEmail, { pageSize: 10 });
        listTimes.push(Date.now() - start);
        await new Promise((resolve) => setTimeout(resolve, 100));
      }
      results.operations.listFiles = {
        times: listTimes,
        average: listTimes.reduce((a, b) => a + b, 0) / listTimes.length,
        min: Math.min(...listTimes),
        max: Math.max(...listTimes),
      };

      // Benchmark 2: Cache performance
      console.log("2. Benchmarking cache performance...");
      const filesResponse = await this.listFiles(userEmail, { pageSize: 5 });

      if (filesResponse.files.length > 0) {
        const testFileId = filesResponse.files[0].id;

        // Cold call
        this.clearCache();
        const coldStart = Date.now();
        await this.getFile(userEmail, testFileId);
        const coldTime = Date.now() - coldStart;

        // Warm calls
        const warmTimes = [];
        for (let i = 0; i < 5; i++) {
          const start = Date.now();
          await this.getFile(userEmail, testFileId);
          warmTimes.push(Date.now() - start);
        }

        results.cachePerformance = {
          coldTime,
          warmTimes,
          averageWarmTime:
            warmTimes.reduce((a, b) => a + b, 0) / warmTimes.length,
          speedup:
            coldTime /
            (warmTimes.reduce((a, b) => a + b, 0) / warmTimes.length),
        };
      }

      results.stats = this.getStats();
      console.log("✅ Benchmark completed!");
    } catch (error) {
      console.error("❌ Benchmark failed:", error.message);
      results.error = error.message;
    }

    return results;
  }
}

// Export singleton instance
export const googleDriveAPI = new GoogleDriveAPI();
