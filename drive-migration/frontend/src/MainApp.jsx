import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import Navigation from './components/Navigation';
import Migration from './pages/Migration';
import UsersOverview from './pages/UsersOverview';
import './MainApp.css';

function MainApp() {
    useEffect(() => {
        // Setup mock API for development
        setupMockApi();
    }, []);

    return (
        <Router>
            <div className="main-app">
                <Navigation />
                <div className="main-content">
                    <Routes>
                        <Route path="/" element={<Navigate to="/migration" replace />} />
                        <Route path="/migration" element={<Migration />} />
                        <Route path="/users" element={<UsersOverview />} />
                    </Routes>
                </div>
            </div>
        </Router>
    );
}

export default MainApp;
