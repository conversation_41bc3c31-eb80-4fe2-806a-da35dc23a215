# Tính năng tải file về theo nguyên tắc sau:

- T<PERSON><PERSON> cập bảng scanned_users trên supabase
- Tạo mỗi user 1 thư mục theo nguyên tắc: <username là tên thư mục>
- Bắt đầu tải file về cho từng user:
  - Query user trong bảng scanned_files để lấy về danh sách file thuộc user đó, chỉ lấy các file chưa migrate (dựa vào cột status)
  - Lấy file id hoặc folder id trong cột file_id, tải file về.
  - Nếu là folder thì tạo mới folder ở local
  - Nếu là file thì tải file về
  - Đảm bảo sau khi tải về cấu trúc file / thư mục vẫn giữ nguyên như trên google drive.
- Hiển thị các thống kê realtime:
  - Người dùng đang tải file
  - Tổng số file / folder cần tải
  - Tiến độ hiện tại
  - Thời gian đã chạy
  - File nào bị lỗi, file nào không.
  - Nếu file bị lỗi, cập nhật nội dung lỗi vào bản ghi supabase, cột error message.
- Tạo một màn hình để thiết lập cấu hình tải file:
  - Chọn danh sách người dùng cần tải (có thể chọn tất cả)
  - Chọn thư mục đích tải về
  - Chọn số tiến trình tải đồng thời
  - Chọn số lần retry khi tải file lỗi
  - Chọn kích thước chunk khi tải file (tối thiểu 10MB, tối đa 100MB)
  - Chọn kích thước chunk khi tải folder (tối thiểu 10MB, tối đa 100MB)
  - Chọn số lần retry khi tải folder lỗi
  - Chọn số lần retry khi tải folder lỗi